/*
 * 来料检验实时看板样式 - 性能优化版本
 * 移除了大量重复和复杂的DatePicker样式，解决鼠标悬停卡死问题
 */

:global(html) {
  font-size: calc(100vw / 192);
}

.boardContainer {
  background-color: #0d1224;
  background-image: url(../../assets/IncomingInspectionDashboard/bac.png);
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;

  &:fullscreen,
  &:-webkit-full-screen,
  &:-moz-full-screen,
  &:-ms-fullscreen {
    transform-origin: center center;
    width: 1920px;
    height: 1080px;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -960px;
    margin-top: -540px;
    overflow: hidden;
    z-index: 9999;

    @media screen and (aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }
    @media screen and (min-aspect-ratio: 16/9) {
      transform: scale(calc(100vh / 1080px));
    }
    @media screen and (max-aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: absolute;
  height: 6rem;
  width: 100%;
  padding: 0 3rem;
  box-sizing: border-box;
  top: 0;
  left: 0;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerLeft {
  margin-right: 12rem;
}

.datePickerWrapper {
  background-image: url(../../assets/IncomingInspectionDashboard/time_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  width: 18rem;
  height: 3rem;
  position: relative;
  overflow: hidden;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent;
      border: none;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-cell-inner {
      width: 100%;
      color: #fff;
      font-size: 1.4rem;
      line-height: 3rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-picker {
      background: transparent;
      border: none;
      height: 100%;
      width: 100%;
      color: #fff;
      font-size: 1.4rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-picker-input {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 1.4rem;
      text-align: center;
      width: 100%;
      height: 100%;
    }
  }
}

// 高性能DatePicker弹窗样式 - 移除不必要的选择器和属性
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e;
  border: 1px solid #1a3a8b;
  border-radius: 4px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  width: 320px;
  min-height: 350px;
  z-index: 9999;
  color: #ffffff;

  .c7n-pro-calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    color: #ffffff;

    button {
      color: #b9d4ff;
      background: transparent;
      border: none;
      cursor: pointer;
      transition: color 0.2s ease;

      &:hover {
        color: #4a90e2;
      }
    }
  }

  .c7n-pro-calendar-body {
    color: #ffffff;

    td {
      .c7n-pro-calendar-date {
        color: #ffffff;
        border-radius: 50%;
        width: 32px;
        height: 32px;
        line-height: 32px;
        cursor: pointer;
        transition: background-color 0.2s ease;
      }

      &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
        background: #4f8efe;
        color: #ffffff;
        font-weight: bold;
      }

      &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
        background: rgba(74, 144, 226, 0.5);
        font-weight: bold;
      }

      &:hover .c7n-pro-calendar-date {
        background-color: rgba(74, 144, 226, 0.3);
      }
    }
  }
}

.headerCenter {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.title {
  background-image: url(../../assets/IncomingInspectionDashboard/title.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 40rem;
  height: 4rem;
  margin: 0 auto;
}

.enTitle {
  font-size: 1.2rem;
  color: #4a90e2;
  margin-top: 0.5rem;
  letter-spacing: 0.2rem;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.timeDisplay {
  text-align: right;
  color: #fff;
}

.currentTime {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.currentWeek {
  font-size: 1.2rem;
  color: #b9d4ff;
}

.fullscreenBtn {
  background: rgba(26, 58, 139, 0.3);
  border: 1px solid rgba(185, 212, 255, 0.3);
  color: #b9d4ff;
  border-radius: 4px;
  padding: 0.8rem 1.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(26, 58, 139, 0.5);
    border-color: #4a90e2;
    color: #fff;
  }
}

// 主要内容区域
.mainContent {
  position: absolute;
  top: 6rem;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 2rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.topRow,
.bottomRow {
  display: flex;
  gap: 2rem;
  flex: 1;
}

.panel {
  background: rgba(12, 26, 62, 0.8);
  border: 1px solid #1a3a8b;
  border-radius: 8px;
  flex: 1;
  position: relative;
  overflow: hidden;
}

.panelHeader {
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #1a3a8b;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panelTitle {
  font-size: 1.6rem;
  font-weight: bold;
  color: #4a90e2;
}

.panelExtra {
  display: flex;
  gap: 1rem;
}

.assetButton {
  background: rgba(26, 58, 139, 0.3);
  border: 1px solid rgba(185, 212, 255, 0.3);
  color: #b9d4ff;
  border-radius: 4px;
  padding: 0.6rem 1.2rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(26, 58, 139, 0.5);
    border-color: #4a90e2;
    color: #fff;
  }
}

.panelBody {
  flex: 1;
  padding: 1.5rem;
  overflow: hidden;
}

.chartPanelBody {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pieChartBackground {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  height: 80%;
  background: radial-gradient(circle, rgba(74, 144, 226, 0.1) 0%, transparent 70%);
  border-radius: 50%;
  z-index: 0;
}

// 时间显示区域
.timeWrapper {
  text-align: right;
  color: #fff;
}

.time {
  font-size: 2.4rem;
  font-weight: bold;
  color: #4a90e2;
  margin-bottom: 0.5rem;
}

.date {
  font-size: 1.4rem;
  color: #b9d4ff;
  margin-bottom: 0.3rem;
}

.week {
  font-size: 1.2rem;
  color: #b9d4ff;
}

// 表格样式
.customTable {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.tableHeader,
.tableHeader2 {
  display: flex;
  background: rgba(26, 58, 139, 0.5);
  border-bottom: 1px solid #1a3a8b;
  padding: 1rem 0;
  font-weight: bold;
  color: #4a90e2;
  font-size: 1.3rem;

  span {
    flex: 1;
    text-align: center;
    padding: 0 1rem;
  }
}

.tableBody {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.tableRow,
.tableRow2 {
  display: flex;
  border-bottom: 1px solid rgba(26, 58, 139, 0.3);
  padding: 1rem 0;
  transition: all 0.3s ease;
  opacity: 0;
  animation: fadeIn 0.5s ease forwards;

  &:hover {
    background: rgba(74, 144, 226, 0.1);
  }

  > * {
    flex: 1;
    text-align: center;
    padding: 0 1rem;
    color: #fff;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.activeRow {
  background: rgba(74, 144, 226, 0.3) !important;
  border-color: #4a90e2 !important;
  animation: pulse 2s ease-in-out;
}

.entering {
  animation: slideInFromRight 0.8s ease forwards;
}

.loadingRow {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  color: #b9d4ff;
  font-size: 1.2rem;
}

.tableCell {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.scrollingText {
  display: inline-block;
  animation: scroll-left 10s linear infinite;
}

.tableIcon {
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
  }
}

.alignRight {
  justify-content: flex-end !important;
  text-align: right !important;
}

.clickableLink {
  color: #4a90e2 !important;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #6bb6ff !important;
  }
}

// 加载指示器旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    background: rgba(74, 144, 226, 0.3);
  }
  50% {
    background: rgba(74, 144, 226, 0.5);
  }
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

// 模态框样式
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.materialFilterModalContainer {
  background: #0c1a3e;
  border: 1px solid #1a3a8b;
  border-radius: 8px;
  width: 600px;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}

.materialFilterModalTitle {
  padding: 2rem;
  border-bottom: 1px solid #1a3a8b;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.titleText {
  font-size: 1.8rem;
  font-weight: bold;
  color: #4a90e2;
}

.titleDecorator {
  color: #b9d4ff;
  font-size: 1.4rem;
}

.materialFilterModalBody {
  flex: 1;
  padding: 2rem;
  overflow: hidden;
}

.materialFilterModalFooter {
  padding: 1.5rem 2rem;
  border-top: 1px solid #1a3a8b;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.modalButton {
  padding: 0.8rem 2rem;
  border: 1px solid #1a3a8b;
  border-radius: 4px;
  background: rgba(26, 58, 139, 0.3);
  color: #b9d4ff;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(26, 58, 139, 0.5);
    border-color: #4a90e2;
    color: #fff;
  }

  &.primary {
    background: #4a90e2;
    border-color: #4a90e2;
    color: #fff;

    &:hover {
      background: #6bb6ff;
      border-color: #6bb6ff;
    }
  }
}

.materialFilterList {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.materialFilterListHeader {
  display: flex;
  background: rgba(26, 58, 139, 0.5);
  border-bottom: 1px solid #1a3a8b;
  padding: 1rem;
  font-weight: bold;
  color: #4a90e2;
  font-size: 1.3rem;

  span {
    flex: 1;
    text-align: center;
  }
}

.materialFilterListBody {
  flex: 1;
  overflow-y: auto;
}

.materialFilterListRow {
  display: flex;
  border-bottom: 1px solid rgba(26, 58, 139, 0.3);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(74, 144, 226, 0.1);
  }

  &.selected {
    background: rgba(74, 144, 226, 0.3);
    border-color: #4a90e2;
  }

  span {
    flex: 1;
    text-align: center;
    color: #fff;
    font-size: 1.2rem;
  }
}

// 全屏模式样式
.simpleFullscreen {
  zoom: calc(100vw / 1920px);
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  z-index: 9999;
}

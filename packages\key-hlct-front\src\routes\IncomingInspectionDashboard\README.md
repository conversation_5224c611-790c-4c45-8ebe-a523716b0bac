# 来料检验实时看板模块文档

## 模块概述

来料检验实时看板（IncomingInspectionDashboard）是一个用于实时监控来料检验状态的数据可视化看板系统。该模块提供了全面的检验进度跟踪、不良统计分析、物料和供应商统计等功能，帮助质量管理人员实时掌握来料检验情况。

## 目录结构

```
IncomingInspectionDashboard/
├── index.tsx                    # 主组件文件
├── index.module.less           # 主样式文件
├── datepicker-fix.module.less  # 日期选择器样式修复
├── pieChartHelper.ts           # 3D饼图辅助函数
├── components/                 # 组件目录
│   └── ECharts.js             # 自定义ECharts组件
├── services/                   # 服务层
│   ├── index.ts               # API接口定义和服务
│   ├── mockData.ts            # 模拟数据
│   └── mockService.ts         # 模拟服务
└── stores/                     # 数据存储（如有）
```

## 核心功能模块

### 1. 时间控制模块
- **功能**: 提供时间范围选择功能
- **默认时间**: 当前时间往前一个月到当前时间
- **特性**: 
  - 动态计算默认时间范围
  - 支持自定义时间范围查询
  - 实时时钟显示

### 2. 进度统计模块
- **功能**: 显示来料检验的整体进度情况
- **数据类型**: `ProgressStatsData`
- **统计维度**:
  - 待检验数量 (pending)
  - 超期数量 (overdue) 
  - 检验中数量 (inProgress)
  - 已完成数量 (completed)
- **可视化**: 3D饼图展示

### 3. 不良统计模块
- **功能**: 统计和展示不良项目的分布情况
- **数据类型**: `DefectiveStatsData[]`
- **统计维度**:
  - 不良项目名称
  - 不良数量
  - 不良比例
- **可视化**: Top5不良项目柱状图

### 4. 不良明细模块
- **功能**: 展示具体的不良检验记录
- **数据类型**: `DefectiveDetailData`
- **包含信息**:
  - 不良项目详情
  - 检验单号
  - 物料信息
  - 供应商信息
  - 检验员信息
  - 创建时间
- **特性**: 
  - 支持分页加载
  - 点击检验单号可跳转详情
  - 新加载数据动画效果

### 5. 物料统计模块
- **功能**: 按物料维度统计检验情况
- **数据类型**: `MaterialStatsData`
- **统计维度**:
  - 物料基本信息
  - 到货批次数量
  - 检验总数
  - 合格数量
  - 合格率
- **特性**:
  - 支持供应商筛选
  - 分页加载
  - 动画效果

### 6. 供应商统计模块
- **功能**: 按供应商维度统计检验情况
- **数据类型**: `SupplierStatsData`
- **统计维度**:
  - 供应商基本信息
  - 到货批次数量
  - 检验总数
  - 合格数量
  - 合格率
- **特性**:
  - 支持物料筛选
  - 分页加载
  - 动画效果

## 技术架构

### 前端技术栈
- **React**: 主框架
- **TypeScript**: 类型安全
- **ECharts**: 数据可视化
- **Moment.js**: 时间处理
- **Choerodon UI Pro**: UI组件库
- **Less**: 样式预处理

### 数据流架构
```
API接口 → 服务层(services) → 状态管理(useState) → UI组件
```

### 状态管理
使用React Hooks进行状态管理：
- `progressStats`: 进度统计数据
- `defectiveStats`: 不良统计数据
- `materialStats`: 物料统计数据
- `supplierStats`: 供应商统计数据
- 各种分页状态和加载状态

## API接口

### 基础配置
- **API基础路径**: `/inja-qms-tznq/v1`
- **组织ID**: 通过 `getCurrentOrganizationId()` 获取

### 主要接口

#### 1. 进度统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/progress-stats/ui
参数: startDate, endDate
返回: ProgressStatsData
```

#### 2. 不良统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/defective-stats/ui
参数: startDate, endDate
返回: DefectiveStatsData[]
```

#### 3. 不良明细接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/defective-detail/ui
参数: startDate, endDate, supplierId?, materialId?, pageNum, pageSize
返回: DefectiveDetailResponse
```

#### 4. 物料统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/material-stats/ui
参数: startDate, endDate, supplierId?, pageNum, pageSize
返回: MaterialStatsResponse
```

#### 5. 供应商统计接口
```typescript
GET /{organizationId}/qms-inspect-iqc-dashboards/supplier-stats/ui
参数: startDate, endDate, materialId?, pageNum, pageSize
返回: SupplierStatsResponse
```

## 数据类型定义

### 核心数据类型
```typescript
// 进度统计数据
interface ProgressStatsData {
  pending: number;      // 待检验
  overdue: number;      // 超期
  inProgress: number;   // 检验中
  completed: number;    // 已完成
}

// 不良统计数据
interface DefectiveStatsData {
  name: string;         // 不良项目名称
  count: number;        // 不良数量
  ratio: number;        // 不良比例
}

// 物料统计数据
interface MaterialStatsData {
  materialId: number;
  materialCode: string;
  material: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}

// 供应商统计数据
interface SupplierStatsData {
  supplierId: number;
  supplierCode: string;
  supplier: string;
  arrivalBatchCount: number;
  totalInspections: number;
  passedInspections: number;
  passRateNum: number;
  passRate: string;
}
```

### 扩展类型
为了支持前端状态管理，定义了扩展类型：
```typescript
interface ExtendedSupplierStatsData extends SupplierStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}

interface ExtendedMaterialStatsData extends MaterialStatsData {
  id?: number;
  isNewlyLoaded?: boolean;
}
```

## 特色功能

### 1. 3D饼图可视化
- 使用ECharts GL实现3D饼图效果
- 支持鼠标悬停和选中交互
- 动态数据更新

### 2. 分页加载机制
- 支持无限滚动加载
- 新数据加载动画效果
- 加载状态管理

### 3. 数据筛选联动
- 物料和供应商数据相互筛选
- 动态更新筛选选项
- 保持筛选状态

### 4. 全屏显示
- 支持看板全屏模式
- 响应式布局适配
- 快捷键支持

### 5. 实时时钟
- 实时显示当前时间
- 星期显示
- 格式化时间展示

## 样式特性

### 1. 深色主题
- 深蓝色背景色调
- 科技感UI设计
- 高对比度文字

### 2. 响应式设计
- 适配不同屏幕尺寸
- 弹性布局
- 组件自适应

### 3. 动画效果
- 数据加载动画
- 悬停交互效果
- 平滑过渡动画

## 开发和维护

### 开发环境配置
1. 确保已安装所需依赖
2. 配置API接口地址
3. 启动开发服务器

### Mock数据支持
- 提供完整的Mock数据服务
- 支持开发环境调试
- 模拟真实API响应

### 错误处理
- API请求错误处理
- 数据异常处理
- 用户友好的错误提示

### 性能优化
- 使用React.memo优化渲染
- 防抖处理用户交互
- 图表实例复用

## 使用说明

### 基本使用
1. 选择查询时间范围（默认为最近一个月）
2. 查看各项统计数据
3. 点击相关数据进行详细查看
4. 使用筛选功能精确查询

### 交互操作
- 点击检验单号跳转详情页面
- 使用物料/供应商筛选功能
- 滚动加载更多数据
- 全屏查看看板

### 注意事项
- 确保网络连接正常
- 注意时间范围选择的合理性
- 大数据量查询可能需要等待时间

## 扩展开发

### 添加新的统计维度
1. 在services中定义新的数据类型
2. 添加对应的API接口
3. 在主组件中添加状态管理
4. 实现UI展示组件

### 自定义图表类型
1. 在components中创建新的图表组件
2. 配置ECharts选项
3. 集成到主看板中

### 样式定制
1. 修改index.module.less中的样式变量
2. 添加新的样式类
3. 确保响应式兼容性

---

*文档版本: v1.0*  
*最后更新: 2025-08-01*

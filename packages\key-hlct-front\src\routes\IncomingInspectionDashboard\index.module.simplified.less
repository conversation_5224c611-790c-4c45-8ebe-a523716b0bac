/*
 * 来料检验实时看板样式 - 性能优化版本
 * 移除了大量重复和复杂的DatePicker样式，解决鼠标悬停卡死问题
 */

:global(html) {
  font-size: calc(100vw / 192);
}

.boardContainer {
  background-color: #0d1224;
  background-image: url(../../assets/IncomingInspectionDashboard/bac.png);
  background-position: top center;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  color: #fff;
  height: 100vh;
  position: relative;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', sans-serif;

  &:fullscreen,
  &:-webkit-full-screen,
  &:-moz-full-screen,
  &:-ms-fullscreen {
    transform-origin: center center;
    width: 1920px;
    height: 1080px;
    position: fixed;
    top: 50%;
    left: 50%;
    margin-left: -960px;
    margin-top: -540px;
    overflow: hidden;
    z-index: 9999;

    @media screen and (aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }
    @media screen and (min-aspect-ratio: 16/9) {
      transform: scale(calc(100vh / 1080px));
    }
    @media screen and (max-aspect-ratio: 16/9) {
      transform: scale(calc(100vw / 1920px));
    }
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  position: absolute;
  height: 6rem;
  width: 100%;
  padding: 0 3rem;
  box-sizing: border-box;
  top: 0;
  left: 0;
}

.headerLeft,
.headerRight {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.headerLeft {
  margin-right: 12rem;
}

.datePickerWrapper {
  background-image: url(../../assets/IncomingInspectionDashboard/time_bac.png);
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  width: 18rem;
  height: 3rem;
  position: relative;
  overflow: hidden;

  :global {
    .c7n-pro-calendar-picker-wrapper {
      background: transparent;
      border: none;
      height: 100%;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-cell-inner {
      width: 100%;
      color: #fff;
      font-size: 1.4rem;
      line-height: 3rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-picker {
      background: transparent;
      border: none;
      height: 100%;
      width: 100%;
      color: #fff;
      font-size: 1.4rem;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .c7n-pro-calendar-picker-input {
      background: transparent;
      border: none;
      color: #fff;
      font-size: 1.4rem;
      text-align: center;
      width: 100%;
      height: 100%;
    }
  }
}

// 简化的DatePicker弹窗样式 - 只保留核心功能
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e;
  border: 1px solid #1a3a8b;
  border-radius: 4px;
  padding: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
  min-width: 320px;
  width: 320px;
  min-height: 350px;
  z-index: 9999;

  * {
    color: #ffffff;
  }

  .c7n-pro-calendar-header {
    display: flex;
    visibility: visible;
    color: #ffffff;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;

    button {
      color: #b9d4ff;
      background: transparent;
      border: none;
      cursor: pointer;

      &:hover {
        color: #4a90e2;
      }
    }
  }

  .c7n-pro-calendar-body tbody td {
    color: #ffffff;

    .c7n-pro-calendar-date {
      color: #ffffff;
      border-radius: 50%;
      width: 32px;
      height: 32px;
      line-height: 32px;
      cursor: pointer;
    }

    &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
      background: #4f8efe;
      color: #ffffff;
      font-weight: bold;
    }

    &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
      background: rgba(74, 144, 226, 0.5);
      font-weight: bold;
    }

    &:hover .c7n-pro-calendar-date {
      background-color: rgba(74, 144, 226, 0.3);
    }
  }
}

.headerCenter {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.title {
  background-image: url(../../assets/IncomingInspectionDashboard/title.png);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  width: 40rem;
  height: 4rem;
  margin: 0 auto;
}

.enTitle {
  font-size: 1.2rem;
  color: #4a90e2;
  margin-top: 0.5rem;
  letter-spacing: 0.2rem;
}

.headerRight {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.timeDisplay {
  text-align: right;
  color: #fff;
}

.currentTime {
  font-size: 1.8rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.currentWeek {
  font-size: 1.2rem;
  color: #b9d4ff;
}

.fullscreenBtn {
  background: rgba(26, 58, 139, 0.3);
  border: 1px solid rgba(185, 212, 255, 0.3);
  color: #b9d4ff;
  border-radius: 4px;
  padding: 0.8rem 1.5rem;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(26, 58, 139, 0.5);
    border-color: #4a90e2;
    color: #fff;
  }
}

// 加载指示器旋转动画
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
